<template>
  <div class="page-wrapper page-all-tickets">
    <div class="ticket-switch-btn">
      <div class="ticket-user-filter">
        <!-- Replace the select dropdown with the v-switch -->
        <v-switch
          v-model="isMyTickets"
          inset
          color="primary"
          :label="isMyTickets === true ? 'My Tickets' : 'Team Tickets'"
          hide-details
          class="custom-switch"
          @change="fetchData(true)"
        />
      </div>

      <v-switch
        v-model="isKanbanView"
        inset
        color="primary"
        :label="isKanbanView ? 'Card View' : 'List View'"
        hide-details
        class="custom-switch"
      />
    </div>
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between" v-if="isKanbanView">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.allTicket.title }}
            </h2>
            <v-chip size="x-small" class="data-count ml-2">
              Showing {{ tickets.length }} of Records {{ totalTickets }}
            </v-chip>
          </div>
          <v-breadcrumbs :items="items" class="custom-breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
      </v-row>
      <!-- <v-row align="center" justify="space-between" v-else>
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">Ticket Lists</h2>
            <v-chip size="x-small" class="data-count ml-2">
              Showing {{ tickets.length }} of {{ totalTickets }} Records
            </v-chip>
          </div>
          <v-breadcrumbs :items="items" class="breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col cols="auto" class="page-action">
        
        </v-col>
      </v-row> -->
    </div>

    <!-- Page Content Section -->
    <section
      class="page-inner-content page-content-wrapper"
      v-if="isKanbanView"
    >
      <div v-if="loading" class="text-center">
        <v-progress-circular indeterminate color="primary" />
      </div>
      <v-row>
        <v-col>
          <!-- If no tickets across all statuses -->

          <div v-if="!loading && isAllColumnsEmpty" class="no-data-available">
            <Nodata />
          </div>
          <!-- Ticket columns rendered only when there's at least one ticket -->

          <div v-else ref="boardContainer" class="all-ticket-container">
            <div
              v-for="(status, index) in filteredStatusOptions"
              :key="index"
              class="ticket-status-wrapper"
              :class="
                'ticket-status-' +
                status.text.toLowerCase().replace(/\s+/g, '-')
              "
              @dragover="onDragOver"
              @drop="onDrop($event, status.value, status.text)"
            >
              <h3 class="ticket-status-title">{{ status.text }}</h3>
              <div class="scroll-content">
                <div class="card-container">
                  <div
                    v-for="ticket in filteredTickets(status.value)"
                    :key="ticket.ticket_id"
                  >
                    <!-- v-tooltip wraps the entire card -->
                    <v-tooltip
                      location="top"
                      activator="parent"
                      max-width="300"
                      class="message-content"
                    >
                      {{ ticket.description }}
                    </v-tooltip>

                    <v-card
                      class="single-ticket-card"
                      draggable="true"
                      @dragstart="onDragStart($event, ticket)"
                      @dragend="onDragEnd"
                      @click="
                        !isWatcher(ticket)
                          ? editTicketPage(ticket.ticket_id)
                          : null
                      "
                    >
                      <v-card-title class="ticket-title">
                        <h5>{{ ticket.title }}</h5>
                      </v-card-title>
                      <v-card-text class="ticket-description">
                        <!-- You can remove the description here if you only want it in the tooltip -->
                        <!-- <p>{{ ticket.description }}</p> -->

                        <span
                          class="ticket-date-badge"
                          :class="{
                            'ticket-expired':
                              remainingTime[ticket.ticket_id] === 'Expired',
                          }"
                        >
                          <v-icon icon="mdi-calendar" size="small" />
                          {{ remainingTime[ticket.ticket_id] }}
                        </span>

                        <div class="single-ticket-card-footer">
                          <small
                            >{{ strings.allTicket.ticketIds
                            }}{{ ticket.ticket_id }}</small
                          >
                          <span class="profile">
                            <!-- <template v-if="ticket.created_by?.profile_pic">
                                  <img
                                    :src="`${API_BASE_URL}${ticket.created_by.profile_pic}`"
                                    class="profile-pic"
                                  />
                                </template> -->

                            <!-- <template v-else> -->
                            {{
                              ticket.created_by?.first_name
                                .charAt(0)
                                .toUpperCase()
                            }}
                            <!-- </template> -->
                            <v-tooltip activator="parent" location="bottom">
                              {{ ticket.created_by_fullname }}
                            </v-tooltip>
                          </span>
                        </div>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>
    </section>

    <!-- List View -->
    <section class="list-view" v-else>
      <TicketList :isMyTickets="isMyTickets" />
    </section>
  </div>
</template>

<script lang="ts">
import {
  ref,
  onMounted,
  reactive,
  defineComponent,
  watch,
  onUnmounted,
  computed,
} from "vue";
import Button from "../../components/Button.vue";
import {
  getLocations,
  getStatus,
  getTickets,
  getUserById,
  apiClient,
} from "../../api/apiClient";
import Batch from "../../components/Batch.vue";
import router from "../../router";
import Filter from "../../components/Filter.vue";
import { debounce } from "lodash-es";
import strings from "../../../src/assets/strings.json";
import { useToast } from "vue-toastification";
import { API_BASE_URL } from "../../api/apiconfig";
import { useRouter } from "vue-router";
import TicketList from "../../pages/TicketManagement/ticketlist.vue";
import Nodata from "../../components/Nodata.vue";
import { useRoute } from "vue-router";
// Define interfaces for your data structures
export interface Ticket {
  ticket_id: number;
  attachement: any[]; // Adjust type if needed
  assigned_to_fullname: string | null;
  created_by_fullname: string;
  priority_names: string;
  category_name: string | null;
  subcategory_name: string | null;
  status_name: string;
  project_name: string | null;
  status: number;
  location_name: string;
  approved_by_name: string | null;
  cancelled_by_name: string | null;
  created_by: CreatedBy;
  title: string;
  type: string | null;
  description: string;
  watchers: number[];
  due_date: string | null;
  due_expiry_reason: string | null;
  created_at: string;
  updated_at: string;
  solved_at: string | null;
  closed_at: string | null;
  approvel_message: string | null;
  cancel_message: string | null;
  is_approved: boolean;
  justification: string;
  auto_closed: boolean;
  is_deleted: boolean;
  deleted_at: string | null;
  project: number;
  category: number | null;
  subcategory: number | null;
  priority: number;
  location: string;
  updated_by: number | null;
  assigned_to: number | null;
  approvel_status: string | null;
  approved_by: number | null;
  cancelled_by: number | null;
  deleted_by: number | null;
}

export interface CreatedBy {
  id: number;
  role: string;
  location: string;
  profile_pic: string | null;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  fcm_token: string;
  new_user: boolean;
  last_login: string | null;
  date_joined: string;
  is_staff: boolean;
  is_superuser: boolean;
  last_used_reset_token: string | null;
  created_by: number | null;
  updated_by: number;
  groups: any[]; // Adjust if you have a group type
  user_permissions: any[]; // Adjust if you have a permission type
}


export default defineComponent({
  name: "AllTickets",
  components: {
    Button,
    Batch,
    Filter,
    TicketList,
    Nodata,
  },

  setup() {
    // Get users
    // const user = JSON.parse(localStorage.getItem("user") || "{}");

    const tickets = ref<any>([]);
    // const tickets= Array.from({ length: 50 }, (k, v) => v + 1),
    const totalTickets = ref<number>(0);

    const originalTickets = ref<Ticket[]>([]); // To store the original data for resetting filters
    const locations = ref("");
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const errorMessage = ref("");
    const noData = ref(false);
    const loading = ref(false);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = "all";
    const toast = useToast();
    const route = useRoute();
    const searchQuery = ref<string>("");
    const page = ref(1);
    const statusOptions = ref<any[]>([]);
    const requestorOptions = ref<string[]>([]);
    const assignedOptions = ref<any>([]);

    const isKanbanView = ref<boolean>(true);

    // Fetch user details
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    console.log("user", user);
    const userProfile = ref<any>(user.profile_pic);
    console.log(userProfile);

    // Reactive state
    const remainingTime = ref<Record<number, string>>({});
    let intervalId: number | undefined;
    const boardContainer = ref<HTMLElement | null>(null);
    let scrollInterval: number | null = null;
    const router = useRouter();
    const isMyTickets = ref(true); // true = my_tickets, false = team_ticket

    const fetchData = async (isInitialLoad = false) => {
      if (loading.value || loadingMore.value || !hasMore.value) return;
      loading.value = isInitialLoad;
      loadingMore.value = !isInitialLoad;
      errorMessage.value = "";
      if (isInitialLoad) {
        tickets.value = [];
        noData.value = false;
        currentPage.value = 1;
      }
      const params: {
        page: number;
        page_size: string | number;
        role: any;
        user_id: any;
        my_ticket?: number;
        team_ticket?: number;
      } = {
        page: currentPage.value,
        page_size: pageSize,
        role: user.role,
        user_id: user.id,
      };
      if (isMyTickets.value) {
        params.my_ticket = 1;
        // params.page_size = 1000000  
      } else {
        params.team_ticket = 1;
      }

      try {
        const response = await apiClient.get("tickets-all/", {
          params,
        });

        if (response.data.status === 1) {
          const ticketsData = response.data.data || [];

          if (isInitialLoad) {
            tickets.value = ticketsData;
          } else {
            tickets.value = [...tickets.value, ...ticketsData];
          }

          totalTickets.value = ticketsData.length;
          currentPage.value++;
          hasMore.value = ticketsData.length > 0;
        } else {
          console.error("Error fetching tickets:", response.data.message);
          toast.error("Failed to fetch tickets.");
        }
      } catch (error: any) {
        console.error("Error fetching tickets:", error);

        if (error.response?.status === 404) {
          // Redirect to custom 404 page
          router.push("/404");
        } else if (error.response?.status === 401) {
          // Optional: handle session expiration
          router.push("/session-expired");
        } else {
          toast.error("Error loading tickets.");
        }
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const updateCountdown = () => {
      tickets.value.forEach((ticket: any) => {
        if (!ticket.due_date) {
          remainingTime.value[ticket.ticket_id] = "None";
          return;
        }

        // Parse due date as Local Time (if stored in local time format)
        const dueDate = new Date(ticket.due_date);

        // Get current local time
        const nowUTC = new Date(); // This is UTC
        const nowLocal = new Date(
          nowUTC.getTime() - nowUTC.getTimezoneOffset() * 60000
        );

        const diff = dueDate.getTime() - nowLocal.getTime();

        if (isNaN(dueDate.getTime()) || diff <= 0) {
          remainingTime.value[ticket.ticket_id] = "Expired";
          return;
        }

        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        remainingTime.value[
          ticket.ticket_id
        ] = `${hours}h ${minutes}m ${seconds}s`;
      });
    };

    const getRemainingTime = (due_date: string | null) => {
      if (!due_date) return "None"; // If no due date, return "None"

      const dueDate = new Date(due_date).getTime();
      const now = Date.now();
      const diff = dueDate - now;

      if (isNaN(dueDate) || diff <= 0) return "Expired"; // If invalid or expired

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      return `${hours}h ${minutes}m ${seconds}s`;
    };

    const filteredTickets = (statusValue: any) => {
      const result = tickets.value.filter((ticket: any) => {
        if (
          user.role === "R003" ||
          user.role === "R004" ||
          user.role === "R005"
        ) {
          return ticket.status !== 0 && ticket.status === statusValue;
        }
        return ticket.status === statusValue;
      });

      return result;
    };

    const filteredStatusOptions = computed(() => {
      let filtered = statusOptions.value;

      if (["R003", "R004", "R005"].includes(user.role)) {
        filtered = statusOptions.value.filter(
          (status) => filteredTickets(status.value).length > 0
        );
      }

      return filtered;
    });

    const isAllColumnsEmpty = computed(() => {
      if (loading.value) return false; // Don't allow NoData to show while loading
      return filteredStatusOptions.value.every(
        (status) => filteredTickets(status.value).length === 0
      );
    });

    const isWatcher = (ticket: any) => {
      return ticket.watchers?.some(
        (watcher: { id: number }) => watcher.id === user.id
      );
    };

    // Drag event handlers
    const onDragStart = (event: DragEvent, ticket: Ticket) => {
      // Prevent dragging if status is "Awaiting Approval" or ticket ID is 8
      if (ticket.status === 8 || ticket.status === 7) {
        event.preventDefault(); // Prevent the drag
        const reason = ticket.status === 7 ? "closed" : "awaiting approval";
        toast.warning(`You cannot drag this ticket. It is in ${reason}.`);
        return;
      }

      event.dataTransfer?.setData("ticketId", ticket.ticket_id.toString());
    };

    const onDragOver = (event: DragEvent) => {
      event.preventDefault();

      const container = boardContainer.value;
      if (!container) return;

      const { clientX } = event;
      const { left, right } = container.getBoundingClientRect();
      const scrollAmount = 20; // Adjust scrolling speed

      if (clientX < left + 100) {
        // Scroll left
        if (!scrollInterval) {
          scrollInterval = window.setInterval(() => {
            container.scrollLeft -= scrollAmount;
          }, 50);
        }
      } else if (clientX > right - 100) {
        // Scroll right
        if (!scrollInterval) {
          scrollInterval = window.setInterval(() => {
            container.scrollLeft += scrollAmount;
          }, 50);
        }
      } else {
        // Stop scrolling
        if (scrollInterval) {
          clearInterval(scrollInterval);
          scrollInterval = null;
        }
      }
    };

    // Stop scrolling when the user releases the ticket
    const onDragEnd = () => {
      if (scrollInterval) {
        clearInterval(scrollInterval);
        scrollInterval = null;
      }
    };

    // Reintroduce `onDrop` to update the ticket
    const onDrop = async (
      event: DragEvent,
      newStatus: number,
      newStatusName: string
    ) => {
      event.preventDefault();
      onDragEnd(); // Stop any ongoing scrolling

      // Block dropping on status 7
      if (newStatus === 7) {
        toast.warning("You cannot drop tickets directly into Closed status.");
        return;
      }

      const ticketId = event.dataTransfer?.getData("ticketId");
      if (!ticketId) return;

      const ticket = tickets.value.find(
        (t: any) => t.ticket_id === Number(ticketId)
      );
      if (!ticket) return;

      if (ticket.status === newStatus) return;

      const allowedRoles = ["R001", "R002", "R006"];
      const hasFullAccess = allowedRoles.includes(user.role);
      const isMovingSolvedToClosed = ticket.status === "6" && newStatus === 7;

      if (!hasFullAccess && !isMovingSolvedToClosed) {
        toast.error(
          `You are only allowed to move tickets from "solved" to "closed".`
        );
        return;
      }

      // Update ticket locally
      ticket.status = newStatus;
      ticket.status_name = newStatusName;

      try {
        await apiClient.put(`tickets/${ticket.ticket_id}/`, {
          status: newStatus,
          created_by: ticket.created_by,
          project: ticket.project,
          title: ticket.title,
          description: ticket.description,
          updated_by: user.id,
          due_expiry_reason: ticket.due_expiry_reason,
        });

        toast.success(
          `Ticket-${ticket.ticket_id} moved to ${ticket.status_name}`
        );
      } catch (error: any) {
        console.error("Error updating ticket status:", error);
        toast.error(`Error updating ticket status ${ticket.status_name}`);
      }
    };

    onMounted(async () => {
      const stored = localStorage.getItem("isKanbanView");
      isKanbanView.value = stored === "true"; // load saved preference

      fetchData(); // Load first page
      updateCountdown();
      intervalId = setInterval(updateCountdown, 1000);
      // setTimeout(() => {
      //   loading.value = false;
      // }, 2000); // Or however long your actual data fetching takes
    });

    onUnmounted(() => {
      if (intervalId) clearInterval(intervalId);
    });

    watch(isKanbanView, (val) => {
      localStorage.setItem("isKanbanView", String(val)); // save on change
    });

    const updateTickets = (newData: Ticket[]) => {
      tickets.value = newData;
    };

    onMounted(async () => {
      loading.value = true;
      errorMessage.value = "";
      noData.value = false;
      try {
        // Fetch locations and status data
        const locationData = await getLocations();
        const statusData = await getStatus();

        // Fetch all tickets
        const response = await apiClient.get<Ticket[]>("tickets/");

        const ticketData = response.data;

        // Fetch all users at once
        const allUsersResponse = await apiClient.get("/users/");
        const allUsers = allUsersResponse.data.data;

        // Create sets to track unique requestors and assignees
        const uniqueRequestors = new Map();
        const uniqueAssignees = new Map();

        // Map requestor and assignee data while ensuring uniqueness
        ticketData.forEach((ticket: any) => {
          // Add requestor if not already in the set
          if (!uniqueRequestors.has(ticket.created_by)) {
            const requestor = allUsers.find(
              (user: any) => user.id === ticket.created_by
            );
            if (requestor) {
              uniqueRequestors.set(requestor.id, {
                value: requestor.id,
                text: requestor.first_name + requestor.last_name,
              });
            }
          }

          // Add assignee if not already in the set
          if (!uniqueAssignees.has(ticket.assigned_to)) {
            const assignee = allUsers.find(
              (user: any) => user.id === ticket.assigned_to
            );
            if (assignee) {
              uniqueAssignees.set(assignee.id, {
                value: assignee.id,
                text: assignee.first_name + assignee.last_name,
              });
            }
          }
        });

        // Convert Map values to arrays for the dropdowns
        requestorOptions.value = Array.from(uniqueRequestors.values());
        assignedOptions.value = Array.from(uniqueAssignees.values());

        // Map location data
        locationOptions.value = locationData.data.map((location: any) => ({
          value: location.location_id,
          text: location.location_name,
        }));

        // Map status data
        statusOptions.value = statusData.map(
          (status: { id: number; name: string }) => ({
            value: status.id,
            text: status.name,
          })
        );

        // Set filters with mapped options
        filters.value = [
          {
            id: "status",
            placeholder: "-- Select Status --",
            options: statusOptions.value,
          },
          {
            id: "location",
            placeholder: "-- Select Location --",
            options: locationOptions.value,
          },
          {
            id: "created_by",
            placeholder: "-- Select Requester --",
            options: requestorOptions.value,
          },
          {
            id: "assigned_to",
            placeholder: "-- Select Assignee --",
            options: assignedOptions.value,
          },
        ];
        if (ticketData) {
          noData.value = ticketData.length === 0;
        } else {
          noData.value = true;
        }
      } catch (error: any) {
        console.error("Error fetching data: ", error);
        if (error.response?.status === 404) {
          // Redirect to custom 404 page
          router.push("/404");
        } else if (error.response?.status === 401) {
          // Optional: handle session expiration
          router.push("/session-expired");
        } else {
          toast.error("Error loading tickets.");
        }
      } finally {
        loading.value = false;
      }
    });

    const editTicketPage = (ticket_id: number) => {
      router
        .push({
          name: "/TicketManagement/editTicket",
          params: { ticket_id }, // Convert to string as route params are strings
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            console.error("Navigation Error:", err);
          }
        });
    };
    const filters = ref([
      {
        id: "status",
        placeholder: "-- Select Status --",
        options: [],
      },
      {
        id: "location",
        placeholder: "-- Select Location --",
        options: [],
      },
      {
        id: "created_by",
        placeholder: "-- Requster --",
        options: [],
      },
      {
        id: "assigned_to",
        placeholder: "-- Assigned --",
        options: [],
      },
    ]);

    // Initialize search and filters
    const searchValue = ref<string>("");
    const filterParams = ref<Record<string, string | number | null>>({});

    // Debounce search API call to reduce requests
    const debouncedSearch = debounce(() => {
      fetchData(); // Fetch with latest search & filter params
    }, 300);

    // Watch search input and apply debounce
    watch(searchValue, () => {
      debouncedSearch();
    });

    const updateFilterValues = (values: { [key: string]: string }) => {
      filterParams.value = { ...values }; // Update filter parameters
    };

    onMounted(async () => {
      if (route.query.view === "list") {
        isKanbanView.value = false;
        try {
          if (route.query.status) {
            filterParams.value.status = route.query.status as string;
          }
        } catch (error: any) {
          console.error("Error in initialization:", error);
        }
      }
    });

    return {
      tickets,
      locations,
      locationOptions,
      // filters,
      searchQuery,
      statusOptions,
      requestorOptions,
      assignedOptions,
      editTicketPage,
      // resetFilters,
      searchValue,
      filters,
      updateFilterValues,
      user,
      noData,
      loading,
      errorMessage,
      updateTickets,
      totalTickets,
      loadingMore,
      // load,
      strings,
      filteredTickets,
      filteredStatusOptions,
      isAllColumnsEmpty,
      isWatcher,
      onDragStart,
      onDragOver,
      onDragEnd,
      onDrop,
      boardContainer,
      isKanbanView,
      remainingTime,
      getRemainingTime,
      userProfile,
      API_BASE_URL,
      isMyTickets,
      fetchData,
    };
  },
  // computed: {
  //   filteredStatusOptions() {
  //     return this.statusOptions.filter(
  //       (status: any) => this.filteredTickets(status.value).length > 0
  //     );
  //   },
  // },

  data: () => ({
    items: [
      { title: "Home", disabled: false, href: "/" },
      {
        title: "Ticket Management",
        disabled: false,
        href: "/all-tickets",
      },
      {
        title: "All Tickets",
        disabled: false,
        href: "",
      },
    ],
  }),
});
</script>

<style scoped>
.custom-breadcrumbs {
  color: #8c8b90;
  font-size: 12px;
}
.v-breadcrumbs {
  padding: 0% !important;
}
.v-breadcrumbs--density-default {
  padding: 0% !important;
}
.breadcrums-heading {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between !important;
  /* width: 77%; */
}

/* toggle bar */

.custom-switch {
  transform: scale(0.8); /* Scales down the switch */
  margin-top: -10px; /* Adjust as needed */
  margin-bottom: -10px; /* Adjust as needed */
}

.custom-switch .v-input__control {
  min-height: 32px; /* Reduces input height */
}

.custom-switch .v-label {
  font-size: 12px; /* Smaller label font */
}

h1 {
  font-size: 27px;
  font-weight: 700;
  margin: 0;
}

.card {
  overflow-x: scroll;
  background: white;
  border: 2px solid #d8d8d8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  /* width: 77%; */
  padding: 12px 24px;
  cursor: pointer;
}

.custom-tooltip {
  max-width: 100px;
  white-space: pre-wrap;
  overflow: hidden;
}

.message-content {
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-word;
  text-align: left;
}

/* Container Scroll */
.all-ticket-container {
  display: grid;
  grid-auto-flow: column;
  overflow-x: scroll;
  padding-bottom: 16px;
  height: 70vh;
  gap: 0;
  max-width: min-content;
  /* white-space: nowrap; */
}

/* Card Container */

/* Ticket Card */
.ticket-card {
  background: #fff;
}

.ticket-title {
  font-size: 14px;
  font-weight: bold;
}
/* .ticket-description {
  height: 40px;
  padding: 4px;
} */

.hover-content {
  display: none;
}
.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.custom-card-item {
  padding: 10px;
  display: flex;
  /* justify-content: space-between !important; */
  align-items: center;
  width: 100%;
  gap: 40px;
}
.custom-card {
  /* padding: 6px; */
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.custom-card span {
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
}
.custom-card p {
  font-size: 14px;
  font-weight: 500;
  color: #2e2e2e;
}

.filter-fields {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  justify-content: space-evenly;
}

.action-btn {
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  gap: 12px;
}
.view-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #026bb1;
  border-radius: 50%;
  color: #026bb1;
}
.edit-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #2dcc70;
  border-radius: 50%;
  color: #2dcc70;
}
.custom-btn {
  background: none;
  border: 1px solid #2dcc70;
  border-radius: 5px;
  color: #2dcc70;
  box-shadow: none !important;
  margin: 0;
  text-transform: none;
}
.high-btn {
  background: none;
  border: 1px solid #ff7780;
  border-radius: 50px;
  color: #ff7780;
  box-shadow: none !important;
  margin: 0;
  padding: 0%;
  text-transform: none;
  max-width: 57px;
}

.filters-wrapp {
  margin-top: 20px;
  /* width: 77%; */
  display: flex;
  flex-wrap: wrap;
  background-color: #f9f9f9;
  padding: 12px;
  align-items: center;
  gap: 12px;
}

.search-icon {
  background: #2dcc70;
  color: #fff;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.profile {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #026bb1;
  margin: 4px;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* .search-icon:hover {
  color: #2dcc70;
  background: #fff;
} */
.cached-icon {
  background: #e6e6e6;
  color: #b5b5b5;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Custom styles for inputs */

input,
.v-select__selections,
.v-text-field input {
  background: transparent;
  outline: none;
  box-shadow: none;
  font-size: 14px;
}

.v-select__control,
.v-text-field__control {
  border-radius: 12px; /* Same border radius for select */
  border: 1px solid #d8d8d8; /* Same border color */
}

.v-select__control:focus,
.v-text-field__control:focus {
  border-color: #2dcc70; /* Same focus color */
}
.v-icon {
  font-size: 14px !important;
}
.v-list-item__append > .v-icon {
  font-size: 14px !important;
}
.no-data-available {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  height: 200px; /* you can adjust */
  text-align: center;
  color: #999;
}
</style>
