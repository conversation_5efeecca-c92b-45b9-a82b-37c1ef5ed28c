<template>
  <div class="chart-examples">
    <h2>Chart Component Examples</h2>
    
    <!-- Progress Bar Example -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title>Progress Bar Chart</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="progress"
              title="Ticket Status Progress"
              :data="progressData"
              :height="200"
              :show-header="true"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- ApexCharts Bar Chart -->
    <v-row class="mb-4">
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>ApexCharts Bar Chart</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="apex"
              apex-type="bar"
              title="Monthly Tickets"
              :labels="monthlyLabels"
              :series="monthlyBarSeries"
              :height="350"
              :show-year-dropdown="true"
              :years="availableYears"
              @year-changed="onYearChanged"
            />
          </v-card-text>
        </v-card>
      </v-col>
      
      <!-- ApexCharts Pie Chart -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>ApexCharts Pie Chart</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="apex"
              apex-type="pie"
              title="Tickets by Category"
              :labels="categoryLabels"
              :series="categorySeries"
              :height="350"
              :options="pieChartOptions"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Chart.js Examples -->
    <v-row class="mb-4">
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>Chart.js Line Chart</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="chartjs"
              chartjs-type="line"
              title="Ticket Trends"
              :labels="trendLabels"
              :datasets="lineDatasets"
              :height="300"
              :options="lineChartOptions"
            />
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>Chart.js Doughnut Chart</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="chartjs"
              chartjs-type="doughnut"
              title="Priority Distribution"
              :data="priorityData"
              :height="300"
              :colors="customColors"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Mixed Chart Example -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title>Mixed Chart (Chart.js)</v-card-title>
          <v-card-text>
            <ChartComponent
              chart-type="chartjs"
              chartjs-type="bar"
              title="Tickets Overview"
              :labels="mixedLabels"
              :datasets="mixedDatasets"
              :height="400"
              :options="mixedChartOptions"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import ChartComponent from './ChartComponent.vue';

export default defineComponent({
  name: 'ChartExamples',
  components: {
    ChartComponent
  },
  setup() {
    // Progress Bar Data
    const progressData = ref([
      { label: 'Open Tickets', value: 45, percentage: 75, color: '#FF6384' },
      { label: 'In Progress', value: 30, percentage: 50, color: '#36A2EB' },
      { label: 'Resolved', value: 60, percentage: 100, color: '#4BC0C0' },
      { label: 'Closed', value: 25, percentage: 42, color: '#FFCE56' }
    ]);

    // ApexCharts Data
    const monthlyLabels = ref(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']);
    const monthlyBarSeries = ref([
      {
        name: 'Open Tickets',
        data: [30, 40, 45, 50, 49, 60]
      },
      {
        name: 'Closed Tickets',
        data: [25, 35, 40, 45, 44, 55]
      }
    ]);

    const categoryLabels = ref(['Hardware', 'Software', 'Network', 'Security', 'Other']);
    const categorySeries = ref([44, 55, 13, 43, 22]);

    // Chart.js Data
    const trendLabels = ref(['Week 1', 'Week 2', 'Week 3', 'Week 4']);
    const lineDatasets = ref([
      {
        label: 'New Tickets',
        data: [12, 19, 3, 5],
        borderColor: '#FF6384',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        fill: true
      },
      {
        label: 'Resolved Tickets',
        data: [8, 15, 7, 9],
        borderColor: '#36A2EB',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        fill: true
      }
    ]);

    const priorityData = ref([
      { label: 'High', value: 25 },
      { label: 'Medium', value: 45 },
      { label: 'Low', value: 30 }
    ]);

    // Mixed Chart Data
    const mixedLabels = ref(['Q1', 'Q2', 'Q3', 'Q4']);
    const mixedDatasets = ref([
      {
        type: 'bar',
        label: 'Tickets Created',
        data: [120, 150, 180, 200],
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      },
      {
        type: 'line',
        label: 'Resolution Rate',
        data: [85, 90, 88, 95],
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        fill: false,
        yAxisID: 'y1'
      }
    ]);

    // Configuration
    const availableYears = ref([2022, 2023, 2024]);
    const customColors = ref(['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']);

    // Chart Options
    const pieChartOptions = ref({
      legend: {
        position: 'bottom'
      }
    });

    const lineChartOptions = ref({
      responsive: true,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    });

    const mixedChartOptions = ref({
      responsive: true,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          grid: {
            drawOnChartArea: false,
          },
        }
      }
    });

    // Event Handlers
    const onYearChanged = (year: number) => {
      console.log('Year changed to:', year);
      // Here you would typically fetch new data based on the selected year
    };

    return {
      progressData,
      monthlyLabels,
      monthlyBarSeries,
      categoryLabels,
      categorySeries,
      trendLabels,
      lineDatasets,
      priorityData,
      mixedLabels,
      mixedDatasets,
      availableYears,
      customColors,
      pieChartOptions,
      lineChartOptions,
      mixedChartOptions,
      onYearChanged
    };
  }
});
</script>

<style scoped>
.chart-examples {
  padding: 20px;
}

h2 {
  margin-bottom: 30px;
  color: #333;
}
</style>
