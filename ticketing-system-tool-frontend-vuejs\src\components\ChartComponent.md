# ChartComponent Documentation

A versatile Vue.js component that supports multiple chart types including ApexCharts, Chart.js, and custom progress bars.

## Features

- **Multiple Chart Libraries**: Supports both ApexCharts and Chart.js
- **Custom Progress Bars**: Built-in progress bar visualization
- **Responsive Design**: Automatically adapts to different screen sizes
- **Year Dropdown**: Optional year selection functionality
- **Type Safety**: Full TypeScript support with proper interfaces
- **Flexible Data Input**: Accepts data in multiple formats

## Installation

The component is already set up in your project. Just import it:

```vue
<script>
import ChartComponent from '@/components/ChartComponent.vue';

export default {
  components: {
    ChartComponent
  }
}
</script>
```

## Basic Usage

### 1. Progress Bar Chart

```vue
<ChartComponent
  chart-type="progress"
  title="Ticket Status Progress"
  :data="progressData"
  :height="250"
/>
```

**Data Format:**
```javascript
const progressData = [
  { label: 'Open Tickets', value: 45, percentage: 75, color: '#FF6384' },
  { label: 'In Progress', value: 30, percentage: 50, color: '#36A2EB' },
  { label: 'Resolved', value: 60, percentage: 100, color: '#4BC0C0' }
];
```

### 2. ApexCharts Bar Chart

```vue
<ChartComponent
  chart-type="apex"
  apex-type="bar"
  title="Monthly Tickets"
  :labels="['Jan', 'Feb', 'Mar', 'Apr']"
  :series="barSeries"
  :height="350"
  :show-year-dropdown="true"
  :years="[2022, 2023, 2024]"
  @year-changed="onYearChanged"
/>
```

**Series Format:**
```javascript
const barSeries = [
  {
    name: 'Open Tickets',
    data: [30, 40, 45, 50]
  },
  {
    name: 'Closed Tickets',
    data: [25, 35, 40, 45]
  }
];
```

### 3. ApexCharts Pie Chart

```vue
<ChartComponent
  chart-type="apex"
  apex-type="pie"
  title="Tickets by Category"
  :labels="['Hardware', 'Software', 'Network']"
  :series="[44, 55, 13]"
  :height="350"
/>
```

### 4. Chart.js Line Chart

```vue
<ChartComponent
  chart-type="chartjs"
  chartjs-type="line"
  title="Ticket Trends"
  :labels="['Week 1', 'Week 2', 'Week 3', 'Week 4']"
  :datasets="lineDatasets"
  :height="300"
/>
```

**Datasets Format:**
```javascript
const lineDatasets = [
  {
    label: 'New Tickets',
    data: [12, 19, 3, 5],
    borderColor: '#FF6384',
    backgroundColor: 'rgba(255, 99, 132, 0.2)',
    fill: true
  }
];
```

### 5. Chart.js with Simple Data

```vue
<ChartComponent
  chart-type="chartjs"
  chartjs-type="doughnut"
  title="Priority Distribution"
  :data="priorityData"
  :colors="['#FF6384', '#36A2EB', '#FFCE56']"
/>
```

**Simple Data Format:**
```javascript
const priorityData = [
  { label: 'High', value: 25 },
  { label: 'Medium', value: 45 },
  { label: 'Low', value: 30 }
];
```

## Props Reference

### Chart Configuration
- `chart-type`: `'apex' | 'chartjs' | 'progress'` - Type of chart to render
- `apex-type`: `'line' | 'area' | 'bar' | 'pie' | 'donut' | 'radialBar'` - ApexCharts type
- `chartjs-type`: `'line' | 'bar' | 'pie' | 'doughnut' | 'radar'` - Chart.js type

### Dimensions
- `height`: `string | number` - Chart height (default: 350)
- `width`: `string | number` - Chart width (default: '100%')

### Data Props
- `data`: `ChartDataItem[]` - Simple data format for basic charts
- `labels`: `string[]` - Chart labels
- `series`: `ApexSeriesItem[]` - ApexCharts series data
- `datasets`: `ChartJSDataset[]` - Chart.js datasets
- `options`: `object` - Custom chart options

### Header Configuration
- `title`: `string` - Chart title
- `show-header`: `boolean` - Show/hide header (default: true)
- `show-year-dropdown`: `boolean` - Show year selector
- `years`: `number[]` - Available years for dropdown
- `default-year`: `number` - Default selected year

### Styling
- `colors`: `string[]` - Custom color palette
- `progress-height`: `number` - Progress bar height (default: 20)

## Events

- `@year-changed`: Emitted when year selection changes
- `@chart-ready`: Emitted when chart is initialized

## Advanced Examples

### Mixed Chart (Chart.js)

```vue
<ChartComponent
  chart-type="chartjs"
  chartjs-type="bar"
  title="Tickets Overview"
  :labels="['Q1', 'Q2', 'Q3', 'Q4']"
  :datasets="mixedDatasets"
  :options="mixedOptions"
/>
```

```javascript
const mixedDatasets = [
  {
    type: 'bar',
    label: 'Tickets Created',
    data: [120, 150, 180, 200],
    backgroundColor: 'rgba(255, 99, 132, 0.6)'
  },
  {
    type: 'line',
    label: 'Resolution Rate',
    data: [85, 90, 88, 95],
    borderColor: 'rgba(54, 162, 235, 1)',
    yAxisID: 'y1'
  }
];

const mixedOptions = {
  scales: {
    y: { type: 'linear', position: 'left' },
    y1: { type: 'linear', position: 'right' }
  }
};
```

## Best Practices

1. **Use appropriate chart types** for your data
2. **Provide meaningful titles** and labels
3. **Use consistent color schemes** across your application
4. **Handle loading states** while fetching data
5. **Implement error handling** for API failures
6. **Test responsiveness** on different screen sizes

## Troubleshooting

- Ensure all required dependencies are installed
- Check console for TypeScript errors
- Verify data format matches expected interface
- Use browser dev tools to inspect chart rendering
